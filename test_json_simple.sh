#!/bin/bash

echo "测试新的JSON解析实现..."

# 创建一个模拟您之前遇到问题的响应
cat > /tmp/test_response.json << 'EOF'
{"model":"deepseek-r1:32b","created_at":"2025-07-03T08:03:51.349219649Z","message":{"role":"assistant","content":"\u003cthink\u003e\n用户问了一个问题，我需要给出有用的回答。\n\u003c/think\u003e\n\n您好！我是DeepSeek-R1，一个AI助手。我可以帮助您回答问题、解决问题、进行对话等。有什么我可以为您做的吗？"},"done_reason":"stop","done":true,"total_duration":4868541876}
EOF

echo "测试响应内容："
cat /tmp/test_response.json | head -c 200
echo "..."
echo ""

# 使用jq验证JSON格式是否正确
if command -v jq &> /dev/null; then
    echo "验证JSON格式..."
    if jq . /tmp/test_response.json > /dev/null 2>&1; then
        echo "✓ JSON格式正确"

        # 提取content字段
        content=$(jq -r '.message.content' /tmp/test_response.json)
        echo "✓ 成功提取content字段"
        echo "Content内容："
        echo "$content"

        # 检查Unicode转义是否被正确处理
        if echo "$content" | grep -q "<think>"; then
            echo "✓ Unicode转义序列被正确处理"
        else
            echo "✗ Unicode转义序列处理失败"
        fi

    else
        echo "✗ JSON格式错误"
    fi
else
    echo "! jq命令不可用，跳过JSON验证"
fi

# 清理
rm -f /tmp/test_response.json

echo ""
echo "新的JSON解析实现应该能够："
echo "1. 正确解析完整的JSON响应"
echo "2. 自动处理Unicode转义序列"
echo "3. 提供详细的错误信息"
echo "4. 处理不完整或损坏的响应"