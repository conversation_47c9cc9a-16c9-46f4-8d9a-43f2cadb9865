# MCP本地Agent集成说明

## 概述

本项目已成功集成了MCP (Model Context Protocol) 功能，使AI可以通过工具调用来操作本地文件系统。这实现了一个真正的本地agent，用户可以通过自然语言与AI对话来完成文件操作任务。

## 架构说明

### 组件结构
1. **C++ MCP客户端** (`MCPClient.h/cpp`)
   - 负责与MCP服务器通信
   - 实现JSON-RPC协议
   - 管理工具调用和响应

2. **Python MCP服务器** (`mcp_file_server.py`)
   - 提供文件系统操作工具
   - 实现安全的路径验证
   - 支持读取、写入、列表、删除、创建目录等操作

3. **集成的AI聊天界面** (`AiChatDialog.h/cpp`)
   - 解析AI响应中的工具调用
   - 执行MCP工具并显示结果
   - 提供完整的对话体验

### 工作流程
1. 用户启动程序，同时启动MCP服务器
2. AI收到包含工具描述的系统提示
3. 用户发送文件操作请求
4. AI生成包含工具调用的JSON响应
5. 程序解析并执行工具调用
6. 将执行结果显示给用户

## 可用工具

### 1. read_file
- **功能**: 读取文件内容
- **参数**: `path` (文件路径)
- **示例**: "请读取config.txt文件的内容"

### 2. write_file
- **功能**: 写入文件内容
- **参数**: `path` (文件路径), `content` (文件内容)
- **示例**: "请创建一个名为hello.txt的文件，内容是'Hello World'"

### 3. list_directory
- **功能**: 列出目录内容
- **参数**: `path` (目录路径)
- **示例**: "请列出当前目录的所有文件"

### 4. delete_file
- **功能**: 删除文件或目录
- **参数**: `path` (文件/目录路径)
- **示例**: "请删除temp.txt文件"

### 5. create_directory
- **功能**: 创建目录
- **参数**: `path` (目录路径)
- **示例**: "请创建一个名为backup的目录"

## 安全特性

1. **路径验证**: 只允许访问指定的目录范围
2. **路径遍历防护**: 防止`../`等危险路径操作
3. **权限控制**: 基于启动时指定的允许路径列表
4. **错误处理**: 完善的错误捕获和用户友好的错误信息

## 使用方法

### 启动程序
```bash
cd build
./mini_tool
```

### 对话示例
- **用户**: "请列出当前目录的文件"
- **AI**: 会调用`list_directory`工具并显示文件列表

- **用户**: "请创建一个名为notes.txt的文件，内容是今天的学习笔记"
- **AI**: 会调用`write_file`工具创建文件

- **用户**: "请读取刚才创建的notes.txt文件"
- **AI**: 会调用`read_file`工具并显示文件内容

## 扩展性

### 添加新工具
1. 在`mcp_file_server.py`中添加新的工具定义
2. 实现对应的处理函数
3. 更新工具列表

### 集成其他MCP服务
1. 启动额外的MCP服务器
2. 在`AiChatDialog.cpp`中添加多服务器支持
3. 合并工具列表

## 技术细节

### MCP协议实现
- 使用JSON-RPC 2.0协议
- 支持工具发现和调用
- 实现了完整的初始化握手

### 错误处理
- 网络通信错误
- 工具执行错误
- JSON解析错误
- 文件系统权限错误

### 性能优化
- 工具列表缓存
- 异步通信
- 连接复用

## 故障排除

### 常见问题
1. **MCP服务器启动失败**: 检查Python3是否安装
2. **文件访问被拒绝**: 检查路径是否在允许范围内
3. **工具调用失败**: 检查JSON格式是否正确

### 调试方法
1. 查看控制台输出
2. 检查MCP服务器日志
3. 验证工具参数格式

## 未来改进

1. **更多工具类型**: 网络请求、系统信息等
2. **图形化配置**: 可视化的权限和路径管理
3. **多语言支持**: 支持其他编程语言的MCP服务器
4. **云端集成**: 支持远程MCP服务器

---

这个实现展示了如何将MCP协议集成到现有的C++应用程序中，为AI助手提供了强大的本地操作能力，同时保持了良好的安全性和可扩展性。
