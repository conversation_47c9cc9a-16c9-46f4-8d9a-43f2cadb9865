# AI聊天功能测试指南

## 功能概述
已成功实现智能聊天功能，包含以下特性：

### 1. 确认页面
- 显示GPU资源占用警告
- 详细说明功能影响
- 用户确认后才能继续

### 2. Ollama进程管理
- 自动启动 `ollama run deepseek-r1:32b` 命令
- 检查Ollama服务状态
- 进程状态监控

### 3. 聊天界面
- 上方：AI输出显示区域
- 下方：用户输入框和发送按钮
- 支持回车键发送消息

### 4. API通信
- 使用HTTP POST请求与Ollama API通信
- 端点：http://localhost:11434/api/chat
- 支持聊天历史上下文

## 测试步骤

### 前置条件
1. 确保系统已安装Ollama
2. 确保DeepSeek-R1 32B模型已下载
3. 确保有足够的GPU资源

### 测试流程
1. 启动程序：`./mini_tool`
2. 点击"智能聊天"按钮
3. 阅读警告信息，点击"确认启动"
4. 等待Ollama服务启动（状态显示"正在启动..."）
5. 服务就绪后，输入测试消息
6. 验证AI回复功能

### 测试用例
1. **基本对话测试**
   - 输入："你好"
   - 预期：AI回复问候语

2. **中文对话测试**
   - 输入："请介绍一下你自己"
   - 预期：AI用中文回复自我介绍

3. **技术问题测试**
   - 输入："什么是机器学习？"
   - 预期：AI提供技术解释

## 实现细节

### 文件结构
- `src/AiChatDialog.h` - 头文件
- `src/AiChatDialog.cpp` - 实现文件
- 已集成到MainFrame中

### 关键功能
- 状态管理（确认/启动/聊天）
- JSON请求构建和响应解析
- 聊天历史管理
- UI状态控制

### API格式
```json
{
  "model": "deepseek-r1:32b",
  "messages": [
    {"role": "system", "content": "你是一个有用的AI助手。请用中文回答问题。"},
    {"role": "user", "content": "用户消息"}
  ],
  "stream": false
}
```

## 注意事项
1. 首次启动可能需要较长时间
2. 确保Ollama服务正常运行
3. 大模型需要大量GPU内存
4. 网络连接需要正常

## 故障排除
1. 如果启动失败，检查Ollama安装
2. 如果连接失败，检查API端口11434
3. 如果响应异常，查看原始响应内容
