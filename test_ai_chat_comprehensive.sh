#!/bin/bash

# 综合测试智能聊天界面的脚本
echo "综合测试智能聊天界面..."

# 记录测试开始时间
start_time=$(date)
echo "测试开始时间: $start_time"

# 获取当前dmesg的行数
initial_lines=$(dmesg | wc -l)
echo "初始dmesg行数: $initial_lines"

# 测试1: 正常启动和关闭
echo "=== 测试1: 正常启动和关闭 ==="
for i in {1..3}; do
    echo "第 $i 次正常启动测试..."
    
    # 启动程序
    timeout 3s ./mini_tool &
    pid=$!
    
    # 等待程序启动
    sleep 1
    
    # 正常关闭程序
    kill -TERM $pid 2>/dev/null
    wait $pid 2>/dev/null
    
    echo "完成第 $i 次测试"
done

# 测试2: 强制关闭
echo "=== 测试2: 强制关闭测试 ==="
for i in {1..2}; do
    echo "第 $i 次强制关闭测试..."
    
    # 启动程序
    timeout 3s ./mini_tool &
    pid=$!
    
    # 等待程序启动
    sleep 1
    
    # 强制关闭程序
    kill -KILL $pid 2>/dev/null
    wait $pid 2>/dev/null
    
    echo "完成第 $i 次强制关闭测试"
done

# 测试3: 快速启动关闭
echo "=== 测试3: 快速启动关闭测试 ==="
for i in {1..5}; do
    echo "第 $i 次快速测试..."
    
    # 启动程序
    ./mini_tool &
    pid=$!
    
    # 立即关闭
    sleep 0.1
    kill -TERM $pid 2>/dev/null
    wait $pid 2>/dev/null
    
    echo "完成第 $i 次快速测试"
done

echo "测试完成时间: $(date)"

# 检查是否有新的段错误
final_lines=$(dmesg | wc -l)
if [ $final_lines -gt $initial_lines ]; then
    echo "检查测试期间的所有新日志..."
    new_logs=$(dmesg | tail -n $((final_lines - initial_lines)))
    if echo "$new_logs" | grep -q "segfault.*mini_tool"; then
        echo "❌ 测试期间发现段错误："
        echo "$new_logs" | grep "segfault.*mini_tool"
        exit 1
    else
        echo "✅ 测试期间未发现段错误"
        if [ $final_lines -gt $initial_lines ]; then
            echo "新增的系统日志："
            echo "$new_logs"
        fi
    fi
else
    echo "✅ 测试期间未产生新的系统日志"
fi

echo "✅ 所有测试通过！段错误修复成功！"
