#!/bin/bash

# 测试段错误修复的脚本 v2
echo "测试智能聊天界面段错误修复 v2..."

# 记录测试开始时间
start_time=$(date)
echo "测试开始时间: $start_time"

# 获取当前dmesg的行数，用于后续只检查新的日志
initial_lines=$(dmesg | wc -l)
echo "初始dmesg行数: $initial_lines"

# 运行程序多次，模拟用户操作
for i in {1..3}; do
    echo "第 $i 次测试..."
    
    # 启动程序
    timeout 5s ./mini_tool &
    pid=$!
    
    # 等待程序启动
    sleep 1
    
    # 模拟关闭程序（发送SIGTERM）
    kill -TERM $pid 2>/dev/null
    
    # 等待程序退出
    wait $pid 2>/dev/null
    exit_code=$?
    
    echo "退出代码: $exit_code"
    
    # 检查是否有新的段错误（只检查新增的日志行）
    current_lines=$(dmesg | wc -l)
    if [ $current_lines -gt $initial_lines ]; then
        new_logs=$(dmesg | tail -n $((current_lines - initial_lines)))
        if echo "$new_logs" | grep -q "segfault.*mini_tool"; then
            echo "❌ 检测到新的段错误！"
            echo "$new_logs" | grep "segfault.*mini_tool"
        else
            echo "✅ 未检测到新的段错误"
        fi
    else
        echo "✅ 未检测到新的段错误"
    fi
    
    sleep 1
done

echo "测试完成时间: $(date)"

# 最终检查
final_lines=$(dmesg | wc -l)
if [ $final_lines -gt $initial_lines ]; then
    echo "检查测试期间的所有新日志..."
    new_logs=$(dmesg | tail -n $((final_lines - initial_lines)))
    if echo "$new_logs" | grep -q "segfault.*mini_tool"; then
        echo "❌ 测试期间发现段错误："
        echo "$new_logs" | grep "segfault.*mini_tool"
    else
        echo "✅ 测试期间未发现段错误"
    fi
else
    echo "✅ 测试期间未产生新的系统日志"
fi
