# MCP本地Agent演示命令

## 基础文件操作演示

### 1. 列出目录内容
```
请列出当前目录的所有文件和文件夹
```

### 2. 创建文件
```
请创建一个名为demo.txt的文件，内容是"这是一个MCP演示文件"
```

### 3. 读取文件
```
请读取demo.txt文件的内容
```

### 4. 创建目录
```
请创建一个名为test_folder的目录
```

### 5. 在新目录中创建文件
```
请在test_folder目录中创建一个名为info.txt的文件，内容包含当前时间和系统信息
```

### 6. 列出新目录内容
```
请列出test_folder目录的内容
```

### 7. 删除文件
```
请删除demo.txt文件
```

## 高级操作演示

### 1. 批量文件操作
```
请创建三个文件：file1.txt、file2.txt、file3.txt，每个文件都包含不同的内容
```

### 2. 文件内容分析
```
请读取所有txt文件的内容，并告诉我总共有多少个字符
```

### 3. 目录结构管理
```
请创建一个项目目录结构：project/src、project/docs、project/tests
```

### 4. 配置文件创建
```
请创建一个config.json文件，包含应用程序的基本配置信息
```

### 5. 日志文件管理
```
请创建一个log.txt文件，记录今天的操作历史
```

## 实际应用场景

### 1. 代码项目管理
```
请帮我创建一个C++项目的基本目录结构，包括src、include、build、docs目录
```

### 2. 文档整理
```
请读取所有的.md文件，并创建一个index.txt文件列出所有文档的标题
```

### 3. 备份操作
```
请将所有的.txt文件内容合并到一个backup.txt文件中
```

### 4. 清理操作
```
请删除所有临时文件（以temp_开头的文件）
```

## 测试AI理解能力

### 1. 复杂指令
```
请创建一个学习计划文件，包含本周的学习目标、每日任务和进度跟踪
```

### 2. 条件操作
```
如果存在config.txt文件就读取它，如果不存在就创建一个默认的配置文件
```

### 3. 数据处理
```
请创建一个CSV文件，包含学生姓名、年龄、成绩等信息，然后读取并分析数据
```

## 安全测试

### 1. 路径限制测试
```
请尝试访问上级目录的文件（这应该被拒绝）
```

### 2. 权限测试
```
请尝试创建系统目录（这应该被拒绝）
```

## 使用提示

1. **自然语言**: 可以用自然语言描述你想要的操作，AI会理解并转换为相应的工具调用
2. **批量操作**: 可以在一个请求中要求多个文件操作
3. **条件逻辑**: AI可以根据文件是否存在等条件执行不同的操作
4. **错误处理**: 如果操作失败，AI会提供清晰的错误信息和建议

## 注意事项

1. 所有文件操作都限制在当前工作目录及其子目录中
2. 删除操作请谨慎，确认后再执行
3. 大文件操作可能需要一些时间
4. 如果遇到权限问题，请检查文件和目录的访问权限

---

通过这些演示命令，你可以充分体验MCP本地Agent的强大功能。AI不仅能理解你的自然语言指令，还能智能地选择合适的工具来完成任务。
