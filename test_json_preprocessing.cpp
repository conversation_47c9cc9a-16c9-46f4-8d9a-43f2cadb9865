#include <iostream>
#include <string>
#include "json.hpp"

using json = nlohmann::json;

// 模拟我们的预处理函数
std::string PreprocessJsonString(const std::string& jsonStr) {
    std::string processed = jsonStr;
    
    // 查找content字段的值并修复其中的换行符问题
    size_t contentPos = processed.find("\"content\":");
    if (contentPos != std::string::npos) {
        // 找到content字段的值开始位置
        size_t valueStart = processed.find('"', contentPos + 10);
        if (valueStart != std::string::npos) {
            valueStart++; // 跳过开始的引号
            
            // 找到content字段值的结束位置
            size_t valueEnd = valueStart;
            bool inEscape = false;
            
            for (size_t i = valueStart; i < processed.length(); i++) {
                char ch = processed[i];
                if (inEscape) {
                    inEscape = false;
                } else if (ch == '\\') {
                    inEscape = true;
                } else if (ch == '"') {
                    valueEnd = i;
                    break;
                }
            }
            
            if (valueEnd > valueStart) {
                // 提取content字段的值
                std::string contentValue = processed.substr(valueStart, valueEnd - valueStart);
                
                // 修复content值中的换行符和其他控制字符
                std::string fixedContent = contentValue;
                
                // 替换未转义的换行符
                size_t pos = 0;
                while ((pos = fixedContent.find('\n', pos)) != std::string::npos) {
                    // 检查前面是否已经有反斜杠转义
                    if (pos == 0 || fixedContent[pos-1] != '\\') {
                        fixedContent.replace(pos, 1, "\\n");
                        pos += 2;
                    } else {
                        pos++;
                    }
                }
                
                // 将修复后的content值替换回原JSON
                processed.replace(valueStart, valueEnd - valueStart, fixedContent);
            }
        }
    }
    
    return processed;
}

int main() {
    std::cout << "测试JSON预处理功能..." << std::endl;
    
    // 模拟您遇到的问题响应（包含未转义的换行符）
    std::string problematicJson = R"({"model":"deepseek-r1:32b","created_at":"2025-07-03T08:24:46.533987088Z","message":{"role":"assistant","content":"\u003cthink\u003e
这里有一个未转义的换行符
\u003c/think\u003e

这是AI的回复内容。"},"done":true})";
    
    std::cout << "原始JSON（有问题的）:" << std::endl;
    std::cout << problematicJson.substr(0, 150) << "..." << std::endl;
    std::cout << std::endl;
    
    // 尝试直接解析（应该失败）
    try {
        json directParse = json::parse(problematicJson);
        std::cout << "✗ 直接解析竟然成功了（不应该）" << std::endl;
    } catch (const json::parse_error& e) {
        std::cout << "✓ 直接解析失败（预期的）: " << e.what() << std::endl;
    }
    
    // 使用预处理后再解析
    std::string preprocessed = PreprocessJsonString(problematicJson);
    std::cout << std::endl << "预处理后的JSON:" << std::endl;
    std::cout << preprocessed.substr(0, 150) << "..." << std::endl;
    std::cout << std::endl;
    
    try {
        json parsedJson = json::parse(preprocessed);
        std::cout << "✓ 预处理后解析成功！" << std::endl;
        
        // 提取content字段
        if (parsedJson.contains("message") && 
            parsedJson["message"].contains("content")) {
            std::string content = parsedJson["message"]["content"].get<std::string>();
            std::cout << "✓ 成功提取content字段" << std::endl;
            std::cout << "Content内容:" << std::endl;
            std::cout << content << std::endl;
            
            // 检查Unicode转义是否被正确处理
            if (content.find("<think>") != std::string::npos) {
                std::cout << "✓ Unicode转义序列被正确处理" << std::endl;
            } else {
                std::cout << "! Unicode转义序列可能需要进一步处理" << std::endl;
            }
        } else {
            std::cout << "✗ 没有找到content字段" << std::endl;
        }
        
    } catch (const json::parse_error& e) {
        std::cout << "✗ 预处理后解析仍然失败: " << e.what() << std::endl;
    }
    
    return 0;
}
