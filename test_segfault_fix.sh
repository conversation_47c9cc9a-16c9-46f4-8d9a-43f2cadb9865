#!/bin/bash

# 测试段错误修复的脚本
echo "测试智能聊天界面段错误修复..."

# 记录测试开始时间
start_time=$(date)
echo "测试开始时间: $start_time"

# 清理之前的core文件
sudo rm -f /var/crash/* 2>/dev/null

# 运行程序多次，模拟用户操作
for i in {1..5}; do
    echo "第 $i 次测试..."
    
    # 启动程序
    timeout 10s ./mini_tool &
    pid=$!
    
    # 等待程序启动
    sleep 2
    
    # 模拟关闭程序（发送SIGTERM）
    kill -TERM $pid 2>/dev/null
    
    # 等待程序退出
    wait $pid 2>/dev/null
    exit_code=$?
    
    echo "退出代码: $exit_code"
    
    # 检查是否有新的段错误
    if dmesg | tail -10 | grep -q "segfault.*mini_tool"; then
        echo "❌ 检测到段错误！"
        dmesg | tail -5 | grep "segfault.*mini_tool"
    else
        echo "✅ 未检测到段错误"
    fi
    
    sleep 1
done

echo "测试完成时间: $(date)"
echo "检查系统日志中的段错误记录..."
dmesg | grep "segfault.*mini_tool" | tail -5
