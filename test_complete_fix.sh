#!/bin/bash

echo "Testing the complete AI chat fix..."

# Create a test JSON response that matches what you were getting
cat > /tmp/test_response.json << 'EOF'
{"model":"deepseek-r1:32b","created_at":"2025-07-03T07:56:38.735731718Z","message":{"role":"assistant","content":"\u003cthink\u003e\n 回复还是不对\n\n用户说AI回复解析失败，看起来是JSON解析的问题。让我看看这个响应格式...\n\n这个响应看起来是正常的Ollama API响应格式，包含了model、created_at、message等字段。message.content字段包含了AI的回复内容。\n\n问题可能是：\n1. Unicode转义序列没有正确处理（\\u003c 应该是 <）\n2. JSON解析逻辑有问题\n3. 字符编码问题\n\n让我提供一个解决方案。\n\u003c/think\u003e\n\n我看到您遇到了AI回复解析失败的问题。这个错误通常是由于JSON响应中的Unicode转义序列没有正确处理导致的。\n\n从您提供的响应来看，这是一个正常的Ollama API响应，但其中包含了Unicode转义序列（如\\u003c代表<字符）。\n\n我建议以下解决方案：\n\n1. **改进JSON解析**：确保正确处理Unicode转义序列\n2. **检查字符编码**：确保UTF-8编码正确处理\n3. **验证API响应格式**：确认响应符合预期的JSON结构\n\n您可以尝试重新发送消息，或者检查Ollama服务的配置。如果问题持续存在，可能需要更新JSON解析逻辑。"},"done":true,"done_reason":"stop","total_duration":1234567890,"load_duration":123456789,"prompt_eval_count":50,"prompt_eval_duration":987654321,"eval_count":200,"eval_duration":876543210}
EOF

echo "Test response created. Content preview:"
echo "$(cat /tmp/test_response.json | head -c 200)..."
echo ""

# Test our parsing logic
echo "Testing content extraction..."
content=$(cat /tmp/test_response.json | grep -o '"content":"[^"]*"' | sed 's/"content":"//' | sed 's/"$//')

if [ ! -z "$content" ]; then
    echo "✓ Content field found"
    echo "Raw content (first 100 chars): ${content:0:100}..."
    
    # Test Unicode escape processing
    if echo "$content" | grep -q "\\\\u003c"; then
        echo "✓ Unicode escapes detected"
        processed_content=$(echo "$content" | sed 's/\\u003c/</g' | sed 's/\\u003e/>/g' | sed 's/\\n/\n/g')
        echo "Processed content preview:"
        echo "$processed_content" | head -3
        
        if echo "$processed_content" | grep -q "<think>"; then
            echo "✓ Unicode escape processing successful!"
        else
            echo "✗ Unicode escape processing failed"
        fi
    else
        echo "! No Unicode escapes found"
    fi
else
    echo "✗ Content field not found"
fi

# Clean up
rm -f /tmp/test_response.json

echo ""
echo "Test completed. The fix should now handle this type of response correctly."
