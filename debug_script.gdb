# GDB调试脚本
set confirm off
set pagination off

# 设置断点
break AiChatDialog::SendHttpRequest
break AiChatDialog::ProcessOllamaResponse
break AiChatDialog::PreprocessJsonString
break AiChatDialog::SendMessageToOllamaWithRetry

# 启动程序
run

# 当程序停在断点时的命令
commands 1
  printf "=== SendHttpRequest 被调用 ===\n"
  printf "jsonData: %s\n", jsonData.c_str()
  continue
end

commands 2
  printf "=== ProcessOllamaResponse 被调用 ===\n"
  printf "response 长度: %d\n", response.length()
  printf "response 内容 (前200字符): %.200s\n", response.c_str()
  continue
end

commands 3
  printf "=== PreprocessJsonString 被调用 ===\n"
  printf "输入 JSON 长度: %d\n", jsonStr.length()
  printf "输入 JSON (前200字符): %.200s\n", jsonStr.c_str()
  continue
end

commands 4
  printf "=== SendMessageToOllamaWithRetry 被调用 ===\n"
  printf "message: %s\n", message.c_str()
  printf "retryCount: %d\n", retryCount
  continue
end

# 继续执行
continue
