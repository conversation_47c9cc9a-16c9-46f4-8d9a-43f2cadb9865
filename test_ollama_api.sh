#!/bin/bash

# Test script to verify the Ollama API fix
echo "Testing Ollama API with the fixed approach..."

# Create a test JSON request
cat > /tmp/test_request.json << 'EOF'
{
  "model": "deepseek-r1:32b",
  "messages": [
    {
      "role": "system",
      "content": "你是一个有用的AI助手。请用中文回答问题。"
    },
    {
      "role": "user",
      "content": "你好，请简单介绍一下你自己。"
    }
  ],
  "stream": false
}
EOF

echo "Sending request to Ollama API..."
echo "Request JSON:"
cat /tmp/test_request.json
echo ""
echo "Response:"

# Test the API call using the same method as our fix
curl -s -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  --data-binary @/tmp/test_request.json

echo ""
echo "Test completed."

# Clean up
rm -f /tmp/test_request.json
