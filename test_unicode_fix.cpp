#include <iostream>
#include <string>

// Test function that mimics our Unicode escape processing
void ProcessUnicodeEscapes(std::string& str) {
    // Handle common Unicode escape sequences
    size_t pos = 0;
    while ((pos = str.find("\\u003c", pos)) != std::string::npos) {
        str.replace(pos, 6, "<");
        pos += 1;
    }
    pos = 0;
    while ((pos = str.find("\\u003e", pos)) != std::string::npos) {
        str.replace(pos, 6, ">");
        pos += 1;
    }
    pos = 0;
    while ((pos = str.find("\\u0026", pos)) != std::string::npos) {
        str.replace(pos, 6, "&");
        pos += 1;
    }
    pos = 0;
    while ((pos = str.find("\\u0027", pos)) != std::string::npos) {
        str.replace(pos, 6, "'");
        pos += 1;
    }
    pos = 0;
    while ((pos = str.find("\\u002f", pos)) != std::string::npos) {
        str.replace(pos, 6, "/");
        pos += 1;
    }
}

int main() {
    std::cout << "Testing Unicode escape processing..." << std::endl;
    
    // Test string similar to what we saw in the error
    std::string testContent = "\\u003cthink\\u003e\\n 回复还是不对";
    
    std::cout << "Before processing: " << testContent << std::endl;
    
    // Process escape sequences
    testContent.replace(testContent.find("\\n"), 2, "\n");
    ProcessUnicodeEscapes(testContent);
    
    std::cout << "After processing: " << testContent << std::endl;
    
    // Check if the result is correct
    if (testContent.find("<think>") != std::string::npos) {
        std::cout << "✓ Unicode escape processing successful!" << std::endl;
        std::cout << "✓ The content now contains readable '<think>' tags" << std::endl;
    } else {
        std::cout << "✗ Unicode escape processing failed" << std::endl;
    }
    
    return 0;
}
