#!/bin/bash

# 测试MCP集成的脚本

echo "=== MCP集成测试 ==="

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: 需要Python3来运行MCP服务器"
    exit 1
fi

# 检查MCP服务器文件是否存在
if [ ! -f "mcp_file_server.py" ]; then
    echo "错误: 找不到mcp_file_server.py文件"
    exit 1
fi

# 使MCP服务器可执行
chmod +x mcp_file_server.py

echo "✓ Python3可用"
echo "✓ MCP服务器文件存在"

# 测试MCP服务器是否可以启动
echo "测试MCP服务器启动..."
timeout 5s python3 mcp_file_server.py --allowed-paths . <<EOF &
{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2025-06-18", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0.0"}}}
EOF

if [ $? -eq 0 ] || [ $? -eq 124 ]; then
    echo "✓ MCP服务器可以启动"
else
    echo "❌ MCP服务器启动失败"
    exit 1
fi

# 编译C++项目
echo "编译项目..."
cd build
if make; then
    echo "✓ 项目编译成功"
else
    echo "❌ 项目编译失败"
    exit 1
fi

echo ""
echo "=== 测试完成 ==="
echo "现在可以运行 ./mini_tool 来测试MCP集成功能"
echo ""
echo "使用说明："
echo "1. 启动程序后，点击'确认启动'来启动AI聊天功能"
echo "2. 在聊天中，你可以要求AI执行文件操作，例如："
echo "   - '请列出当前目录的文件'"
echo "   - '请创建一个名为test.txt的文件，内容是Hello World'"
echo "   - '请读取test.txt文件的内容'"
echo "   - '请删除test.txt文件'"
echo ""
echo "AI会自动调用相应的工具来完成这些操作。"
