#include <iostream>
#include <string>
#include <cstdio>
#include <unistd.h>
#include <cstring>

// Simple test function that mimics our fixed SendHttpRequest logic
bool TestHttpRequest(const std::string& jsonData, std::string& response) {
    // Create temporary file to store JSON data, avoiding shell escaping issues
    char tempFileName[] = "/tmp/ollama_request_XXXXXX";
    int fd = mkstemp(tempFileName);
    if (fd == -1) {
        return false;
    }
    
    // Write JSON data to temporary file
    ssize_t written = write(fd, jsonData.c_str(), jsonData.length());
    close(fd);
    
    if (written == -1) {
        unlink(tempFileName);
        return false;
    }

    // Use curl command to send HTTP request, reading data from file
    std::string curlCommand = "curl -s -X POST http://localhost:11434/api/chat -H \"Content-Type: application/json\" --data-binary @" + std::string(tempFileName);

    // Execute curl command and get output
    FILE* pipe = popen(curlCommand.c_str(), "r");
    if (!pipe) {
        unlink(tempFileName);
        return false;
    }

    char buffer[1024];
    response.clear();
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        response += buffer;
    }
    
    int result = pclose(pipe);
    unlink(tempFileName);

    return (result == 0 && !response.empty());
}

int main() {
    std::cout << "Testing HTTP request with Chinese characters..." << std::endl;
    
    // Test JSON with Chinese characters (similar to what the app would send)
    std::string testJson = R"({
  "model": "deepseek-r1:32b",
  "messages": [
    {
      "role": "system",
      "content": "你是一个有用的AI助手。请用中文回答问题。"
    },
    {
      "role": "user",
      "content": "测试：这是一个包含中文字符的消息。"
    }
  ],
  "stream": false
})";

    std::string response;
    bool success = TestHttpRequest(testJson, response);
    
    if (success) {
        std::cout << "✓ HTTP request successful!" << std::endl;
        std::cout << "Response length: " << response.length() << " characters" << std::endl;
        
        // Check if response contains expected JSON structure
        if (response.find("\"content\":") != std::string::npos) {
            std::cout << "✓ Response contains content field" << std::endl;
        } else {
            std::cout << "✗ Response missing content field" << std::endl;
        }
        
        // Check for error indicators
        if (response.find("missing request body") != std::string::npos) {
            std::cout << "✗ Still getting 'missing request body' error" << std::endl;
        } else {
            std::cout << "✓ No 'missing request body' error detected" << std::endl;
        }
        
        std::cout << "\nFirst 200 characters of response:" << std::endl;
        std::cout << response.substr(0, 200) << "..." << std::endl;
        
    } else {
        std::cout << "✗ HTTP request failed" << std::endl;
        return 1;
    }
    
    return 0;
}
